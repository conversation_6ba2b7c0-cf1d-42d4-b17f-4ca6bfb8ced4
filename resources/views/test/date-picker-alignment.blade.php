@extends('layouts.admin')

@section('title', 'Date Picker Alignment Test')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Date Picker Alignment Test</div>
                </div>
                <div class="card-body">
                    <div class="row gy-4">
                        
                        <!-- Test 1: With Label (like in create/edit forms) -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">With Label (Create/Edit Forms)</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Icon should be positioned at 70% from top</p>
                                    <x-custom-date-picker
                                        name="with_label_date"
                                        id="with_label_date"
                                        label="Booking Date"
                                        placeholder="Select date..." />
                                </div>
                            </div>
                        </div>

                        <!-- Test 2: Without Label (like in filter forms) -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Without Label (Filter Forms)</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Icon should be centered vertically (50% from top)</p>
                                    <x-custom-date-picker
                                        name="no_label_date"
                                        id="no_label_date"
                                        placeholder="From Date"
                                        :show-label="false" />
                                </div>
                            </div>
                        </div>

                        <!-- Test 3: Size Comparison with Standard Form Controls -->
                        <div class="col-xl-12">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Size Comparison with Standard Form Controls</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">All inputs should have identical height and styling</p>
                                    <div class="row gy-3">
                                        <div class="col-xl-4">
                                            <label class="form-label">Standard Text Input</label>
                                            <input type="text" class="form-control" placeholder="Standard input">
                                        </div>
                                        <div class="col-xl-4">
                                            <label class="form-label">Standard Date Input</label>
                                            <input type="date" class="form-control">
                                        </div>
                                        <div class="col-xl-4">
                                            <x-custom-date-picker
                                                name="comparison_date"
                                                id="comparison_date"
                                                label="Custom Date Picker"
                                                placeholder="Custom picker" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Test 4: Filter Row Simulation (Updated Reservations Index) -->
                        <div class="col-xl-12">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Updated Filter Row (Reservations Index)</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Simulating the improved filter layout with label and consistent heights</p>
                                    <form>
                                        <div class="row bg-light rounded py-2 align-items-center filter-row">
                                            <!-- Filter Label -->
                                            <div class="col-auto">
                                                <label class="form-label mb-0 fw-semibold text-muted">
                                                    <i class="ti ti-filter me-1"></i>Filters:
                                                </label>
                                            </div>

                                            <!-- Status Filter -->
                                            <div class="col-xl-2 col-lg-3 col-md-6">
                                                <select class="form-select form-select-sm">
                                                    <option>All Statuses</option>
                                                    <option>Confirmed</option>
                                                    <option>Pending</option>
                                                </select>
                                            </div>

                                            <!-- Field Filter -->
                                            <div class="col-xl-2 col-lg-3 col-md-6">
                                                <select class="form-select form-select-sm">
                                                    <option>All Fields</option>
                                                    <option>Field 1</option>
                                                    <option>Field 2</option>
                                                </select>
                                            </div>

                                            <!-- Date From Filter -->
                                            <div class="col-xl-2 col-lg-3 col-md-6">
                                                <x-custom-date-picker
                                                    name="filter_date_from"
                                                    id="filter_date_from"
                                                    placeholder="From Date"
                                                    :show-label="false"
                                                    class="form-control-sm" />
                                            </div>

                                            <!-- Date To Filter -->
                                            <div class="col-xl-2 col-lg-3 col-md-6">
                                                <x-custom-date-picker
                                                    name="filter_date_to"
                                                    id="filter_date_to"
                                                    placeholder="To Date"
                                                    :show-label="false"
                                                    class="form-control-sm" />
                                            </div>

                                            <!-- Action Buttons -->
                                            <div class="col-xl-3 col-lg-12 col-md-12">
                                                <div class="d-flex gap-2 justify-content-end">
                                                    <button type="submit" class="btn btn-info btn-sm flex-fill">
                                                        <i class="ti ti-search me-1"></i>Filter
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm flex-fill">
                                                        <i class="ti ti-refresh me-1"></i>Clear
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Filter Row Styling - Ensure consistent heights and alignment */
    .filter-row {
        min-height: 60px;
    }

    /* Ensure all form controls in filter row have consistent height */
    .filter-row .form-select-sm,
    .filter-row .custom-date-picker-wrapper .flatpickr-input {
        height: calc(1.5em + 0.5rem + 2px) !important;
        padding: 0.25rem 0.5rem !important;
        font-size: 0.875rem !important;
        line-height: 1.5 !important;
    }

    /* Adjust custom date picker wrapper for small size */
    .filter-row .custom-date-picker-wrapper .flatpickr-input {
        min-height: calc(1.5em + 0.5rem + 2px) !important;
    }

    /* Filter label styling */
    .filter-row .form-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0;
        white-space: nowrap;
    }

    /* Ensure proper vertical alignment */
    .filter-row .col-auto,
    .filter-row [class*="col-"] {
        display: flex;
        align-items: center;
    }

    /* Button adjustments for consistent height */
    .filter-row .btn-sm {
        height: calc(1.5em + 0.5rem + 2px);
        padding: 0.25rem 0.75rem;
        font-size: 0.875rem;
        line-height: 1.5;
    }

    /* Dark mode compatibility */
    [data-theme-mode="dark"] .filter-row .form-label {
        color: #adb5bd;
    }
</style>
@endpush
