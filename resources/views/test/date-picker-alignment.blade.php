@extends('layouts.admin')

@section('title', 'Date Picker Alignment Test')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Date Picker Alignment Test</div>
                </div>
                <div class="card-body">
                    <div class="row gy-4">
                        
                        <!-- Test 1: With Label (like in create/edit forms) -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">With Label (Create/Edit Forms)</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Icon should be positioned at 70% from top</p>
                                    <x-custom-date-picker
                                        name="with_label_date"
                                        id="with_label_date"
                                        label="Booking Date"
                                        placeholder="Select date..." />
                                </div>
                            </div>
                        </div>

                        <!-- Test 2: Without Label (like in filter forms) -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Without Label (Filter Forms)</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Icon should be centered vertically (50% from top)</p>
                                    <x-custom-date-picker
                                        name="no_label_date"
                                        id="no_label_date"
                                        placeholder="From Date"
                                        :show-label="false" />
                                </div>
                            </div>
                        </div>

                        <!-- Test 3: Size Comparison with Standard Form Controls -->
                        <div class="col-xl-12">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Size Comparison with Standard Form Controls</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">All inputs should have identical height and styling</p>
                                    <div class="row gy-3">
                                        <div class="col-xl-4">
                                            <label class="form-label">Standard Text Input</label>
                                            <input type="text" class="form-control" placeholder="Standard input">
                                        </div>
                                        <div class="col-xl-4">
                                            <label class="form-label">Standard Date Input</label>
                                            <input type="date" class="form-control">
                                        </div>
                                        <div class="col-xl-4">
                                            <x-custom-date-picker
                                                name="comparison_date"
                                                id="comparison_date"
                                                label="Custom Date Picker"
                                                placeholder="Custom picker" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Test 4: Filter Row Simulation (like reservations index) -->
                        <div class="col-xl-12">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Filter Row Simulation (Reservations Index)</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Simulating the actual filter layout from reservations index</p>
                                    <form>
                                        <div class="row gy-2">
                                            <div class="col-xl-3">
                                                <select class="form-select">
                                                    <option>All Status</option>
                                                    <option>Confirmed</option>
                                                    <option>Pending</option>
                                                </select>
                                            </div>
                                            <div class="col-xl-3">
                                                <select class="form-select">
                                                    <option>All Fields</option>
                                                    <option>Field 1</option>
                                                    <option>Field 2</option>
                                                </select>
                                            </div>
                                            <div class="col-xl-2">
                                                <x-custom-date-picker
                                                    name="filter_date_from"
                                                    id="filter_date_from"
                                                    placeholder="From Date"
                                                    :show-label="false" />
                                            </div>
                                            <div class="col-xl-2">
                                                <x-custom-date-picker
                                                    name="filter_date_to"
                                                    id="filter_date_to"
                                                    placeholder="To Date"
                                                    :show-label="false" />
                                            </div>
                                            <div class="col-xl-2">
                                                <button type="submit" class="btn btn-info btn-sm w-100">
                                                    <i class="ti ti-search me-1"></i>Filter
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
