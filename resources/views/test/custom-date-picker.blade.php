@extends('layouts.admin')

@section('title', 'Custom Date Picker Test')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Custom Date Picker Component Test</div>
                </div>
                <div class="card-body">
                    <div class="row gy-4">
                        
                        <!-- Default Type (No Label) -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Default Type (No Label)</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Default behavior - label is hidden</p>
                                    <x-custom-date-picker
                                        name="default_date"
                                        id="default_date"
                                        placeholder="Select date..." />
                                </div>
                            </div>
                        </div>

                        <!-- Labeled Type -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Labeled Type</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Explicit labeled type - shows label</p>
                                    <x-custom-date-picker
                                        name="labeled_date"
                                        id="labeled_date"
                                        label="Date"
                                        type="labeled"
                                        placeholder="Select date..." />
                                </div>
                            </div>
                        </div>

                        <!-- Required Field with Label -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Required Field with Label</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Required field with red asterisk</p>
                                    <x-custom-date-picker
                                        name="required_date"
                                        id="required_date"
                                        label="Required Date"
                                        type="labeled"
                                        :required="true"
                                        placeholder="Select date..." />
                                </div>
                            </div>
                        </div>

                        <!-- Disabled Field -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Disabled Field</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Disabled state with pre-filled value</p>
                                    <x-custom-date-picker
                                        name="disabled_date"
                                        id="disabled_date"
                                        label="Disabled Date"
                                        type="labeled"
                                        :disabled="true"
                                        value="{{ date('Y-m-d') }}"
                                        placeholder="Select date..." />
                                </div>
                            </div>
                        </div>

                        <!-- Legacy Support (showLabel=false) -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Legacy Support</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Using deprecated showLabel=false (still works)</p>
                                    <x-custom-date-picker
                                        name="legacy_date"
                                        id="legacy_date"
                                        :show-label="false"
                                        placeholder="Select date..." />
                                </div>
                            </div>
                        </div>

                        <!-- With onChange Event -->
                        <div class="col-xl-6">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">With onChange Event</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">Triggers alert on date change</p>
                                    <x-custom-date-picker
                                        name="onchange_date"
                                        id="onchange_date"
                                        label="Event Date"
                                        type="labeled"
                                        :on-change="'alert(\"Date changed to: \" + document.getElementById(\"onchange_date\").value);'"
                                        placeholder="Select date..." />
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Test Results -->
                    <div class="row mt-4">
                        <div class="col-xl-12">
                            <div class="card border">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Test Results</h6>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-primary" onclick="showValues()">
                                        Show All Values
                                    </button>
                                    <div id="test-results" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showValues() {
    const fields = [
        'default_date',
        'labeled_date', 
        'required_date',
        'disabled_date',
        'legacy_date',
        'onchange_date'
    ];
    
    let results = '<h6>Current Values:</h6><ul>';
    
    fields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        const value = element ? element.value : 'Not found';
        results += `<li><strong>${fieldId}:</strong> ${value || 'Empty'}</li>`;
    });
    
    results += '</ul>';
    
    document.getElementById('test-results').innerHTML = results;
}
</script>
@endsection
